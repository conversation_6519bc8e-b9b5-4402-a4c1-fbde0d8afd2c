if (typeof require !== 'undefined') {
    var $ = (typeof window.$ === 'function') ? window.$ : require('jquery');
}

function SystemsightAjaxFormHandler(options) {
    var defaults = {
        version: '1.1.6',
        formSelector: '[data-type="ajax"]',
        submitSelector: 'button[type="submit"]',
        preloaderElement: ' <div class="preloader spinner-border spinner-border-sm" role="status"><span class="sr-only">...</span></div>'
    };
    var that = this;
    options = options || {};

    that.formsIterator = 0;
    that.options = $.extend(true, {}, defaults, options);
    that.init();
}
SystemsightAjaxFormHandler.prototype.init = function () {
    if (typeof $('body').attr('data-systemsightajaxformhandler') !== 'undefined') {
        return;
    }

    var that = this;
    //start listening for submits
    $('body').on('submit', that.options.formSelector, function (e) {
        if (!$(this).attr('id')) {
            $(this).attr('id', 'ss_' + (++that.formsIterator));
        }
        that.handle_form_submit($(this), $(this).data('submit_invoker'));
        return false;
    }).on('click', `${that.options.formSelector} button[type="submit"], ${that.options.formSelector} input[type="submit"][formaction]`, function (e) {
        //sets submit action invoker
        $(this.form).data('submit_invoker', $(this));
    });

    $('body').attr('data-systemsightajaxformhandler', true);//Jei JS failas load'intas >1 kartą
    console.log('SystemsightAjaxFormHandler inited (' + that.options.version + '). MODIFIED');

};

/**
 * @param {Object} $form
 * @param {Object=} $button
 */
SystemsightAjaxFormHandler.prototype.find_submit_btn = function ($form, $button) {
    var that = this;
    if (!$button) {
        $button = $form.find(that.options.submitSelector);
    }
    return $button;
}

/**
 * Kai paspaudė submit'ino formą.
 * @param {Object} $form
 * @param {Object=} $button
 */
SystemsightAjaxFormHandler.prototype.handle_form_submit = function ($form, $button) {
    var that = this;
    that.show_preloader($form, $button);
    that.remove_form_feedbacks($form);

    var formData = $form.serializeArray();

    if ($button && $button.attr('name')) {
        formData.push(
            {
                name: $button.attr('name'),
                value: $button.val()
            }
        );
    }

    var action = $form.attr('action');
    if ($button && $button.attr('formaction')) {
        action = $button.attr('formaction');
    }







    // Multiple services handling

    // Get all service items from form data
    const serviceItems = formData.filter(item => item.name === "relations[Services.Item][]");

    if (serviceItems.length > 1) {
        $form.trigger('ajax-form.before-submit', [$button]);

        // Get all service IDs from the form data
        const allValues = serviceItems.map(item => item.value);

        // Add a counter to the form to track requests
        $form.data('is_multiple_services', true);
        $form.data('multiple_services_total', allValues.length);
        $form.data('multiple_services_completed', 0);
        $form.data('multiple_services_responses', []);

        function processMultipleIds(idArray, action, formData, $form, $button) {
            const that = this;
            const promises = [];

            function processId(id) {
                return new Promise((resolve, reject) => {
                    // Create form data with only the current service ID
                    const currentFormData = formData.filter(item => item.name !== "relations[Services.Item][]");

                    // Add only the current service ID
                    currentFormData.push({
                        name: "relations[Services.Item][]",
                        value: id
                    });

                    $.post(action, currentFormData)
                        .done(function (r) {
                            // console.log('Multiple service response for ID', id, ':', r);
                            // Store the response
                            const responses = $form.data('multiple_services_responses') || [];
                            responses.push(r);
                            $form.data('multiple_services_responses', responses);

                            that.handle_form_submit_done($form, r);
                            $form.trigger('ajax-form.done', [r]);
                            resolve(r);
                        })
                        .fail(function (e) {
                            that.handle_form_submit_fail($form, e);
                            $form.trigger('ajax-form.fail');
                            reject(e);
                        })
                        .always(function (e) {
                            $form = that.get_updated_form_element($form);

                            // Increment the completed counter
                            const completed = $form.data('multiple_services_completed') + 1;
                            $form.data('multiple_services_completed', completed);
                            const total = $form.data('multiple_services_total');

                            // If all requests are completed, handle final response
                            if (completed >= total) {
                                that.hide_preloader($form, $button);
                                that.handle_form_submit_always($form);
                                $form.trigger('ajax-form.always');

                                // Handle redirection from stored redirect URL or last response
                                const storedRedirect = $form.data('multiple_services_redirect');
                                const allResponses = $form.data('multiple_services_responses') || [];
                                const lastResponse = allResponses[allResponses.length - 1];

                                if (storedRedirect) {
                                    // Clean up form data before redirect
                                    $form.removeData('is_multiple_services multiple_services_total multiple_services_completed multiple_services_responses multiple_services_redirect');
                                    window.location.href = storedRedirect;
                                } else if (lastResponse && lastResponse.redirect) {
                                    // Clean up form data before redirect
                                    $form.removeData('is_multiple_services multiple_services_total multiple_services_completed multiple_services_responses multiple_services_redirect');
                                    window.location.href = lastResponse.redirect;
                                } else if (lastResponse && lastResponse.callback) {
                                    eval(lastResponse.callback);
                                } else {
                                    // Fallback: try to redirect to listing page
                                    const currentUrl = window.location.href;
                                    if (currentUrl.includes('/add_') || currentUrl.includes('/edit_')) {
                                        const listingUrl = currentUrl.replace(/\/(add_|edit_)[^\/]*.*/, '/listing_item');
                                        // Clean up form data before redirect
                                        $form.removeData('is_multiple_services multiple_services_total multiple_services_completed multiple_services_responses multiple_services_redirect');
                                        window.location.href = listingUrl;
                                    }
                                }
                            }
                        });
                });
            }

            // Process all IDs
            idArray.forEach(id => {
                promises.push(processId(id));
            });

            return Promise.all(promises);
        }

        // Process all values
        processMultipleIds.call(this, allValues, action, formData, $form, $button);
        return; // Exit early, don't continue with normal processing





    } else {
        $form.trigger('ajax-form.before-submit', [$button]);
        // Default AJAX request

        $.post(action, formData).done(function (r) {
            // console.log('Single service response:', r);
            that.handle_form_submit_done($form, r);
            $form.trigger('ajax-form.done', [r]);
        }).always(function (e) {
            $form = that.get_updated_form_element($form);
            that.hide_preloader($form, $button);
            that.handle_form_submit_always($form);
            $form.trigger('ajax-form.always');
        }).fail(function (e) {
            that.handle_form_submit_fail($form, e);
            $form.trigger('ajax-form.fail');
        });

    }

};








/**
 * @param {Object} $form
 * @param {Object=} $button
 */
SystemsightAjaxFormHandler.prototype.show_preloader = function ($form, $button) {
    var that = this;
    $button = that.find_submit_btn($form, $button);

    if ($button.find('.preloader').length === 0) {
        $button.append(that.options.preloaderElement);
    }
    $form.find(that.options.submitSelector)
        .attr("disabled", "disabled");

    $button.find('.preloader').removeClass('d-none')
        ;
};
/**
 * @param {Object} $form
 * @param {Object=} $button
 */
SystemsightAjaxFormHandler.prototype.hide_preloader = function ($form, $button) {
    var that = this;
    $button = that.find_submit_btn($form, $button);
    $form.find(that.options.submitSelector)
        .attr("disabled", null)
        .find('.preloader').addClass('d-none')
        ;
};
/**
 * Remove errors from fields.
 * @param {Object} $form
 */
SystemsightAjaxFormHandler.prototype.remove_form_feedbacks = function ($form) {
    var that = this;
    $form.find('input,select,textarea')
        .removeClass('is-invalid is-valid')
        .parents('.form-group').removeClass('has-error').find('.invalid-feedback').html('')
        ;

    //jei tai simplemodule forma, tai tabus papildomai perziurim
    if ($form.hasClass('simplemodule-form')) {
        $form.find(".sm-edit-tabs a.nav-link").removeClass('is-invalid');
    }

};
/**
 * @param {Object} $form
 */
SystemsightAjaxFormHandler.prototype.handle_form_submit_always = function ($form) {
    var that = this;
    //reload captcha
    $form.find('input.captcha-input').each(function () {
        $(this).val('');
        if (typeof $(this).data('refresh') === 'function') {
            $(this).data('refresh')();
        }
    });
    //update fancybox
    if ($form.parents('.fancybox-wrap').length) {
        $.fancybox.update()
    }
}
/**
 * @param {Object} $form
 * @param {Object} event
 */
SystemsightAjaxFormHandler.prototype.handle_form_submit_fail = function ($form, event) {
    var that = this;
    that.display_messages($form, {
        message: {
            type: 'danger',
            text: event.status + " " + event.statusText
        }
    });
};





// Add a new method to handle completion of all multiple service requests
SystemsightAjaxFormHandler.prototype.handle_multiple_services_complete = function ($form, responses) {
    // console.log("All multiple service requests completed!", responses);

    // Determine what to do with multiple responses
    const finalResponse = responses[responses.length - 1];

    // Display a summary message
    const successCount = responses.filter(r => r.status === "ok").length;
    const errorCount = responses.length - successCount;

    // Create a summary message
    const summaryMessage = {
        message: {
            type: successCount > 0 ? 'success' : 'danger',
            text: `Processing complete: ${successCount} successful, ${errorCount} failed.`
        }
    };

    this.display_messages($form, summaryMessage);

    // If all responses have the same redirect, use it
    const uniqueRedirects = [...new Set(responses.filter(r => r.redirect).map(r => r.redirect))];
    if (uniqueRedirects.length === 1) {
        window.location.href = uniqueRedirects[0];
    }
};



/**
 * @param {Object} $form
 * @param {Object} response JSON object response
 */
SystemsightAjaxFormHandler.prototype.handle_form_submit_done = function ($form, response) {
    var that = this;




    // Check if this was part of a multiple services submission
    var isMultipleServices = $form.data('is_multiple_services');
    var isComplete = isMultipleServices ?
        ($form.data('multiple_services_completed') === $form.data('multiple_services_total')) :
        false;

    if (isMultipleServices && !isComplete) {
        // Skip redirects for individual responses in multiple services, but store the redirect URL
        var redirect = response.redirect;
        if (redirect) {
            // Store the redirect URL for later use
            $form.data('multiple_services_redirect', redirect);
            delete response.redirect;
        }
    }




    if (typeof response === 'string') {
        that.display_messages($form, {
            message: {
                type: 'danger',
                text: response
            }
        });
        return;
    }

    $form.trigger("on_form_submit_done", [response]);//listens in `preview.js`

    if (response.status == "ok") {
        //
    } else {
        that.mark_errors(response.errors, $form);
    }

    if (typeof response.transformed_data === 'object') {
        for (var fieldName in response.transformed_data) {
            var $field = $form.find('[name="' + fieldName + '"],[data-input_name="' + fieldName + '"]');
            //todo: check radio or checkbox types
            if (
                $field.attr('type')
                && $field.attr('type') != 'file'
            ) {
                $field.val(response.transformed_data[fieldName]);
            }
        }
    }


    if (typeof response.message !== 'undefined') {
        that.display_messages($form, response);
    }

    if (typeof response.modal === 'string') {
        that.display_modal($form, response.modal, response);
    }

    if (typeof response.modal_body === 'string') {
        that.display_modal(
            $form,
            that.get_modal_html(response.modal_body),
            response
        );
    }

    if (typeof response.replace_with === "string") {
        var $r = $(response.replace_with);
        if (response.target) {
            $(response.target).replaceWith($r);
        } else {
            $form.replaceWith($r);
            $form = $r;
        }

    }

    // if (typeof response.redirect === "string") {
    //     window.location.href = response.redirect;
    // }


    // Only redirect if not part of multiple services or if all multiple services are complete
    if (typeof response.redirect === "string" && (!isMultipleServices || isComplete)) {
        window.location.href = response.redirect;
    }

    that.run_form_callback(response)

};

SystemsightAjaxFormHandler.prototype.run_form_callback = function (response) {
    var that = this;
    if (typeof response.callback === 'string') {
        that.eval_callback(response.callback, response.nonce);
    }
}
SystemsightAjaxFormHandler.prototype.eval_callback = function (evalValue, nonce) {
    var $script = $('<script nonce="' + nonce + '">' + evalValue + '</script>')
    $('body').append($script)
    $script.remove();
}

/**
 * @param {Array.<string, string>} errors
 * @param {Object} $form
 */
SystemsightAjaxFormHandler.prototype.mark_errors = function (errors, $form) {
    for (var fieldName in errors) {
        var $field = $form.find('[name="' + fieldName + '"],[name="' + fieldName + '[]"],[data-input_name="' + fieldName + '"]');
        $field.addClass('is-invalid')
            .closest('.form-group')
            .addClass('has-error')
            .find('.invalid-feedback')
            .html(errors[fieldName]);
    }
}
/**
 * @param {Object} $form
 */
SystemsightAjaxFormHandler.prototype.get_updated_form_element = function ($form) {
    if ($form.attr('id')) {
        return $('#' + $form.attr('id')); //Select updated DOM element
    } else {
        return $form;
    }
};
/**
 * @param {Object} $form
 * @param {string} modal modal HTML
 * @param {Object} response JSON Object
 */
SystemsightAjaxFormHandler.prototype.display_modal = function ($form, modal, response) {
    var that = this;
    var $modal = $(modal);
    $('body').append($modal);
    let bsModal = new bootstrap.Modal($modal[0], {});

    var redirect = null;
    //padarom redirecta tik poto kai uzdaromas modal'as
    if (typeof response.redirect === 'string') {
        redirect = response.redirect
        delete response.redirect;
    }
    var callback = null
    //padarom callback tik poto kai uzdaromas modal'as
    if (typeof response.callback === 'string') {
        callback = response.callback
        delete response.callback;
    }

    $modal.on('hidden.bs.modal', function (event) {
        bsModal.dispose()
        $modal.remove();

        if (redirect) {
            window.location.href = redirect;
        }
        if (callback) {
            that.eval_callback(callback, response.nonce)
        }

    })
    bsModal.show()


};
/**
 * @param {Object} $form
 * @param {Object} response JSON Object
 */
SystemsightAjaxFormHandler.prototype.display_messages = function ($form, response) {
    var that = this;
    var modalBody = '';
    if (typeof response.message == "object") {
        if (Array.isArray(response.message)) {
            if (response.message.length) {
                $.each(response.message, function (index, msg) {
                    if (typeof msg == "object") {
                        modalBody += '<div class="alert alert-' + msg.type + '">' + msg.text + '</div>';
                    } else {
                        modalBody += '<div class="alert alert-info">' + msg + '</div>';
                    }
                })
            }

        } else {
            modalBody += '<div class="alert alert-' + response.message.type + '">' + response.message.text + '</div>';
        }
    } else {
        modalBody += '<div class="alert alert-info">' + response.message + '</div>';
    }
    if (modalBody) {
        that.display_modal($form, that.get_modal_html(modalBody), response);
    }
}

/**
 * @param {string} modalBody
 */
SystemsightAjaxFormHandler.prototype.get_modal_html = function (modalBody) {
    var that = this;
    var modalHtml =
        '<div class="modal fade modal-ajax-form" tabindex="-1" aria-hidden="true">'
        + '<div class="modal-dialog">'
        + '<div class="modal-content">'
        + '<div class="modal-body">' + modalBody + '</div>'
        + '<div class="modal-footer">'
        + '<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>'
        + '</div>'
        + '</div>'
        + '</div>'
        + '</div>'
        ;

    return modalHtml;
};


if (typeof module === "object" && typeof module.exports === "object") {
    //node modules support
    module.exports = new SystemsightAjaxFormHandler();
} else {
    var systemsightAjaxFormHandler;
    $(document).ready(function () {
        systemsightAjaxFormHandler = new SystemsightAjaxFormHandler();
    });
}
