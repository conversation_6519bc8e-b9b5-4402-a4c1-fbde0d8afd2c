if(typeof require !== 'undefined'){
    var $ = (typeof window.$ === 'function') ? window.$ : require('jquery');
    if(typeof $.fn.select2 !== 'function') {
        require('select2');
    }
    var select2_lt = require('select2/src/js/select2/i18n/lt');
    var lang = document.documentElement.getAttribute('lang');
    var Sortable = require ('sortablejs/Sortable')
}

class CmsSelect2{
    static init($context = null){
        var inputSelector = "[data-input^='select2']";
        let $inputs = $context ? $context.find(inputSelector) : $(inputSelector);

        $inputs.each(function (index, element) {
            if($(element).data('select2Id')) return;
            $(element).data('select2_instance', new CmsSelect2($(element).data('options'), element))

            /**
             * Hacky fix for a bug in Select2 with jQuery 3.6.0, 3.6.1 new nested-focus "protection"
             * see: https://github.com/select2/select2/issues/5993
             * see: https://github.com/jquery/jquery/issues/4382
             *
             * TODO: Recheck with the select2 GH issue and remove once this is fixed on their side
             */
            $(element).on('select2:open', function(e) {
                document.querySelector(`[aria-controls="select2-${e.target.id}-results"]`).focus();
            });

        });
    }

    constructor(options, element) {
        switch ($(element).data('input')){
            case 'select2':
                this.init_select2(options, element)
                break;
            case 'select2_ajax':
                this.init_select2_ajax(options, element)
                break;
            case 'select2_tags':
                this.init_select2_tags(options, element)
                break;
        }
    }

    init_select2(options, input){
        var c = {
            language: lang === 'lt' ? select2_lt: null,
            templateSelection: this.formatSelectedDropdown,
        };
        $.extend(c, options);
        if(c.sortable){
            var values = $(input).data('value');
            if(values && Array.isArray(values)){
                for(var k in values.reverse()){
                    var $option = $(input).find("option[value='"+values[k] +"']");

                    $(input).prepend($option);
                }
            }
        }

        if(typeof c.dropdownParent === "string"){
            c.dropdownParent = $(c.dropdownParent)
        }

        $(input).select2(c)
            //isjungiam atidaryma po clear
            .on('select2:unselecting', function() {
                $(this).data('unselecting', true);
            }).on('select2:opening', function(e) {
            if ($(this).data('unselecting')) {
                $(this).removeData('unselecting');
                e.preventDefault();
            }
        })
        ;
        if(c.sortable){
            this.do_sortable(input);
        }
        if(c.data_url){
            this.load_selec2_data(input, c);
        }
    }
    init_select2_ajax(options, input){
        var that = this;
        var c = {
            language: lang === 'lt' ? select2_lt: null,
            width: '90%',
            minimumInputLength: 2,
            templateSelection: this.formatSelectedDropdown,
            templateResult: function (repo) {
                return repo.title || repo.text || repo.id;
            },
            ajax: {
                delay: 500,
                dataType: "json",
                data: function(params) {
                    var filters = {
                        q: params.term
                    };

                    if(c.depends_on){
                        $.each(c.depends_on, function (index, fieldId) {
                            filters[fieldId] = that.getDependValue(fieldId);
                        });
                    }

                    return filters
                },
                processResults: function(data, params) {
                    return { results: data };
                }
            },
        };
        $.extend(c, options);
        c.ajax.url = c.url;
        if(c.dataType) {
            c.ajax.dataType = c.dataType;
        }

        if(typeof c.dropdownParent === "string"){
            c.dropdownParent = $(c.dropdownParent)
        }

        $(input).select2(c)
            //isjungiam atidaryma po clear
            .on('select2:unselecting', function() {
                $(this).data('unselecting', true);
            }).on('select2:opening', function(e) {
            if ($(this).data('unselecting')) {
                $(this).removeData('unselecting');
                e.preventDefault();
            }
        });

        if(c.sortable){
            this.do_sortable(input)
        }
    }
    init_select2_tags(options, input){
        var that = this;
        var c = {
            language: lang === 'lt' ? select2_lt: null,
            width: '90%',
            tags: true,
            templateSelection: this.formatSelectedDropdown,
        };
        $.extend(c, options);

        if(c.url){
            c.ajax = {
                url: c.url,
                delay: 500,
                dataType: "json",
                data: function(params) {
                    var filters = {
                        q: params.term
                    };

                    if(c.depends_on){
                        $.each(c.depends_on, function (index, fieldId) {
                            filters[fieldId] = that.getDependValue(fieldId);
                        });
                    }

                    return filters
                },
                processResults: function(data, params) {
                    return { results: data };
                }
            };
            c.templateResult = function (repo) {
                return repo.title || repo.text || repo.id;
            };

            if(c.dataType) {
                c.ajax.dataType = c.dataType;
            }
        }

        if(typeof c.dropdownParent === "string"){
            c.dropdownParent = $(c.dropdownParent)
        }

        $(input).select2(c)
            //isjungiam atidaryma po clear
            .on('select2:unselecting', function() {
                $(this).data('unselecting', true);
            }).on('select2:opening', function(e) {
            if ($(this).data('unselecting')) {
                $(this).removeData('unselecting');
                e.preventDefault();
            }
        });
        if(c.sortable){
            this.do_sortable($(input));
        }

        if(c.data_url){
            this.load_selec2_data(input, c);
        }

        $(input).data('inited', true);
    }

    load_selec2_data(input, c) {
        var that = this;
        var $input = $(input);

        var currentValue = $input.val();
        if(!currentValue || (typeof currentValue === 'object' && currentValue.length == 0)){
            currentValue = $input.data('value');
        }

        function getValuesReady(selectedValues){
            if(selectedValues instanceof Array){
                selectedValues = selectedValues.map(function(v) {
                    return v === null ? null : v.toString()
                });
            }else if(selectedValues){
                selectedValues = [selectedValues]
            }else{
                selectedValues = [];
            }

            return selectedValues;
        }

        function reloadData(selectedValues, firstTime){
            selectedValues = getValuesReady(selectedValues);

            var canLoadData = true;
            if(c.required_dependencies){
                $.each(c.required_dependencies, function (index, fieldId) {
                    var v = that.getDependValue(fieldId);//$("#" + fieldId).val();
                    if(v instanceof Array){
                        if(!v.length){
                            canLoadData = false;
                        }
                    }else{
                        if(!v){
                            canLoadData = false;
                        }
                    }
                });
            }

            if(!canLoadData){
                $input
                    .val('')
                    .prop("disabled", true);
                return;
            }

            var filters = {};
            if(c.depends_on){
                $.each(c.depends_on, function (index, fieldId) {
                    filters[fieldId] = that.getDependValue(fieldId);
                });
            }

            $input.prop("disabled", true);
            $.ajax({
                url: c.data_url,
                data: filters
            }).done(function (data) {
                if(!selectedValues.length && $input.data('value')) {
                    selectedValues = getValuesReady($input.data('value'));
                }
                $input.html('<option style="display: none"></option>');
                
                // Check if auto_select_all_on_load is enabled
                var allValues = [];
                if (c.auto_select_all_on_load) {
                    $.each(data, function(index, value){
                        if(value.children && value.children.length){
                            $.each(value.children, function(c_index, c_value){
                                allValues.push(c_value.id.toString());
                            });
                        }else{
                            allValues.push(value.id.toString());
                        }
                    });
                    selectedValues = allValues;
                }

                // Store all available values for manual selection
                var allAvailableValues = [];
                $.each(data, function(index, value){
                    if(value.children && value.children.length){
                        $.each(value.children, function(c_index, c_value){
                            allAvailableValues.push(c_value.id.toString());
                        });
                    }else{
                        allAvailableValues.push(value.id.toString());
                    }
                });
                $input.data('all-available-values', allAvailableValues);

                // Update select all button state after data reload
                setTimeout(function() {
                    $input.trigger('change.selectall-update');
                }, 100);

                $.each(data, function(index, value){
                    if(value.children && value.children.length){
                        var $optgroup = $('<optgroup label="' + value.title+ '"></optgroup>');
                        $input.append($optgroup);
                        $.each(value.children, function(c_index, c_value){
                            var selected = selectedValues.toString().split(',').indexOf(c_value.id.toString()) > -1;
                            var option = new Option(c_value.title, c_value.id, false, selected);
                            $optgroup.append(option);
                        });
                    }else{
                        var selected = selectedValues.toString().split(',').indexOf(value.id.toString()) > -1;
                        var option = new Option(value.title, value.id, false, selected);
                        $input.append(option);
                    }
                });
                if (data.length === 1) {
                    //select first
                    $input.find('option[value]:first').attr('selected','selected');
                }

                if(firstTime && $input.data('select2').options.options.tags){
                    //for tags, create new inputs for non existing values
                    for (var i in selectedValues){
                        var tag = selectedValues[i];
                        if(!$input.find("option[value='" + tag + "']").length){
                            var tagOption = new Option(tag, tag, false, true);
                            $input.append(tagOption);
                        }
                    }
                }

                $input.prop("disabled", false).change();
            })
        }



        reloadData(currentValue, true);

        if(c.depends_on){
            $.each(c.depends_on, function (index, fieldId) {
                $("#" + fieldId).on('change', function () {
                    reloadData(currentValue, false)
                })
            });
        }
        $input.on('change', function (){
            currentValue = $input.val();
        })

        // Add event handlers for separate "Select All" and "Remove All" buttons
        var fieldId = $input.attr('id').replace('custom_', '');
        var selectAllButtonId = 'select_all_' + fieldId;
        var removeAllButtonId = 'remove_all_' + fieldId;

        // Function to update button visibility based on selection state
        function updateButtonsVisibility() {
            var $selectButton = $('#' + selectAllButtonId);
            var $removeButton = $('#' + removeAllButtonId);

            if ($selectButton.length && $removeButton.length) {
                var currentValues = $input.val() || [];

                if (currentValues.length > 0) {
                    // At least one item is selected - show "Remove All" button
                    $removeButton.show();
                } else {
                    // No items selected - hide "Remove All" button
                    $removeButton.hide();
                }
            }
        }

        // Initial button state update
        updateButtonsVisibility();

        // Update buttons when selection changes
        $input.on('change.selectall change.selectall-update', function() {
            updateButtonsVisibility();
        });

        // "Select All" button click handler
        $(document).off('click', '#' + selectAllButtonId).on('click', '#' + selectAllButtonId, function(e) {
            e.preventDefault();
            var allValues = $input.data('all-available-values') || [];
            if (allValues.length > 0) {
                $input.val(allValues).trigger('change');
            }
        });

        // "Remove All" button click handler
        $(document).off('click', '#' + removeAllButtonId).on('click', '#' + removeAllButtonId, function(e) {
            e.preventDefault();
            $input.val([]).trigger('change');
        });
    }
    do_sortable(input) {
        let $select = $(input)
        let $ul = $select.next(".select2-container").find('.select2-selection__rendered');
        let sortableInstance = new Sortable($ul[0], {
            animation: 150,
            filter: '.select2-search__field, .select2-search--inline',
            direction: 'vertical',
            onEnd: function (evt){
                $( $ul.find(".select2-selection__choice").get().reverse() ).each(function() {
                    let title = $(this).attr("title");
                    let option = $select.find( "option:contains(" + title + ")" );
                    $select.prepend(option);
                });
            }
        });
    }
    getDependValue(fieldId) {
        var $input = $("#" + fieldId);
        if ($input.find('input[type="radio"]').length) {
            return $input.find('input[type="radio"]:checked').val()
        } else {
            return $input.val()
        }
    }
    formatSelectedDropdown(state) {
        var title = state.title || state.text || state.id;

        var editLink = $(state.element).parents('select').attr('data-editurl');
        if (editLink === undefined) {
            return title;
        }
        var id = +state.id;
        if (isNaN(id)) {//dėl tags
            return title;
        }
        editLink += (editLink.match(/\?/) ? '&' : '?') + 'id=' + id;

        var $state = $('<span><a href="" target="_blank" data-fancybox="" data-type="iframe"><i class="fa fa-pencil"></i></a> <span></span></span>');
        // Use .text() instead of HTML string concatenation to avoid script injection issues
        $state.find('span').text(title);
        $state.find('a').attr('href', editLink);

        return $state;
    };
}
CmsSelect2.$ = $;
if (typeof module === "object" && typeof module.exports === "object"){

    CmsSelect2.init()
    //node modules support
    module.exports = CmsSelect2;
}else{
    $(function(){
        CmsSelect2.init()
    });
}
